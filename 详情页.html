<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>謎·魔术酒吧详情页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #0a0a0a;
            color: #ffffff;
            width: 540px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .container {
            width: 100%;
            overflow: hidden;
        }

        /* 通用样式 */
        .section {
            margin-bottom: 30px;
            padding: 20px;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #d4af37, transparent);
        }

        /* 金色强调文字 */
        .gold-text {
            color: #d4af37;
        }

        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        /* 按钮样式 */
        .btn {
            background: linear-gradient(45deg, #d4af37, #f4d03f);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        /* 星级评分 */
        .stars {
            color: #d4af37;
            font-size: 16px;
            margin: 5px 0;
        }

        /* 响应式图片 */
        .responsive-img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            object-fit: cover;
        }

        /* 文字阴影效果 */
        .text-shadow {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        /* 发光效果 */
        .glow {
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
        }

        /* 店铺介绍区域 */
        .shop-intro {
            position: relative;
            height: 270px; /* 540px宽度的2:1比例 */
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 30%, #1a1a1a 100%);
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .shop-intro::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="magic" patternUnits="userSpaceOnUse" width="20" height="20"><circle cx="10" cy="10" r="1" fill="%23d4af37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23magic)"/></svg>');
            opacity: 0.3;
        }

        .shop-intro-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 2;
            width: 90%;
        }

        .shop-intro h1 {
            font-size: 28px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .shop-intro p {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .shop-intro .address {
            font-size: 14px;
            color: #d4af37;
            font-weight: bold;
            margin-top: 20px;
        }

        .shop-intro .address::before {
            content: '📍 ';
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面内容将在这里添加 -->
    </div>
</body>
</html>